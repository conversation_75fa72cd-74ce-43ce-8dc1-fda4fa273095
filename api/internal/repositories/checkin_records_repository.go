package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"
	"time"

	"gorm.io/gorm"
)

// CheckinRecordsRepository 打卡记录表：记录孩子每日训练打卡的详细信息仓储接口
type CheckinRecordsRepository interface {
	Create(checkinRecords *models.CheckinRecords) error
	GetByID(id uint) (*models.CheckinRecords, error)
	Update(id uint, checkinRecords *models.CheckinRecords) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.CheckinRecords, int64, error)
	// 根据孩子ID获取打卡记录列表
	GetByChildID(childID uint, offset, limit int) ([]*models.CheckinRecords, int64, error)
	// 根据孩子ID和训练营ID获取打卡记录列表
	GetByChildIDAndCampID(childID uint, campID uint, offset, limit int) ([]*models.CheckinRecords, int64, error)
	// 根据孩子ID和日期获取打卡记录
	GetByChildAndDate(childID uint, date time.Time) (*models.CheckinRecords, error)
}

// checkinRecordsRepository 打卡记录表：记录孩子每日训练打卡的详细信息仓储实现
type checkinRecordsRepository struct {
	db *gorm.DB
}

// NewCheckinRecordsRepository 创建打卡记录表：记录孩子每日训练打卡的详细信息仓储
func NewCheckinRecordsRepository(db *gorm.DB) CheckinRecordsRepository {
	return &checkinRecordsRepository{
		db: db,
	}
}

// Create 创建打卡记录表：记录孩子每日训练打卡的详细信息
func (r *checkinRecordsRepository) Create(checkinRecords *models.CheckinRecords) error {
	if err := r.db.Create(checkinRecords).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建打卡记录表：记录孩子每日训练打卡的详细信息失败")
	}
	return nil
}

// GetByID 根据ID获取打卡记录表：记录孩子每日训练打卡的详细信息
func (r *checkinRecordsRepository) GetByID(id uint) (*models.CheckinRecords, error) {
	var checkinRecords models.CheckinRecords
	if err := r.db.First(&checkinRecords, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("打卡记录表：记录孩子每日训练打卡的详细信息不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询打卡记录表：记录孩子每日训练打卡的详细信息失败")
	}
	return &checkinRecords, nil
}

// Update 更新打卡记录表：记录孩子每日训练打卡的详细信息
func (r *checkinRecordsRepository) Update(id uint, checkinRecords *models.CheckinRecords) error {
	if err := r.db.Model(&models.CheckinRecords{}).Where("id = ?", id).Updates(checkinRecords).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新打卡记录表：记录孩子每日训练打卡的详细信息失败")
	}
	return nil
}

// Delete 删除打卡记录表：记录孩子每日训练打卡的详细信息
func (r *checkinRecordsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.CheckinRecords{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除打卡记录表：记录孩子每日训练打卡的详细信息失败")
	}
	return nil
}

// List 获取打卡记录表：记录孩子每日训练打卡的详细信息列表
func (r *checkinRecordsRepository) List(offset, limit int) ([]*models.CheckinRecords, int64, error) {
	var checkinRecordss []*models.CheckinRecords
	var total int64

	// 获取总数
	if err := r.db.Model(&models.CheckinRecords{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询打卡记录表：记录孩子每日训练打卡的详细信息总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&checkinRecordss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询打卡记录表：记录孩子每日训练打卡的详细信息列表失败")
	}

	return checkinRecordss, total, nil
}

// GetByChildID 根据孩子ID获取打卡记录列表
func (r *checkinRecordsRepository) GetByChildID(childID uint, offset, limit int) ([]*models.CheckinRecords, int64, error) {
	var checkinRecords []*models.CheckinRecords
	var total int64

	// 构建查询条件
	query := r.db.Model(&models.CheckinRecords{}).Where("child_id = ?", childID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询打卡记录总数失败")
	}

	// 获取列表，按日期倒序
	if err := query.Order("checkin_date DESC").Offset(offset).Limit(limit).Find(&checkinRecords).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询打卡记录列表失败")
	}

	return checkinRecords, total, nil
}

// GetByChildIDAndCampID 根据孩子ID和训练营ID获取打卡记录列表
func (r *checkinRecordsRepository) GetByChildIDAndCampID(childID uint, campID uint, offset, limit int) ([]*models.CheckinRecords, int64, error) {
	var checkinRecords []*models.CheckinRecords
	var total int64

	// 构建查询条件
	query := r.db.Model(&models.CheckinRecords{}).Where("child_id = ? AND camp_id = ?", childID, campID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询打卡记录总数失败")
	}

	// 获取列表，按日期倒序
	if err := query.Order("checkin_date DESC").Offset(offset).Limit(limit).Find(&checkinRecords).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询打卡记录列表失败")
	}

	return checkinRecords, total, nil
}

// GetByChildAndDate 根据孩子ID和日期获取打卡记录
func (r *checkinRecordsRepository) GetByChildAndDate(childID uint, date time.Time) (*models.CheckinRecords, error) {
	var checkinRecord models.CheckinRecords

	// 格式化日期为YYYY-MM-DD格式进行查询
	dateStr := date.Format("2006-01-02")

	if err := r.db.Where("child_id = ? AND DATE(checkin_date) = ?", childID, dateStr).First(&checkinRecord).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("打卡记录不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询打卡记录失败")
	}

	return &checkinRecord, nil
}
