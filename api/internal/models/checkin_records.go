package models

import (
	"time"
)

// ==================== Model ====================

// CheckinRecords 打卡记录表：记录孩子每日训练打卡的详细信息
type CheckinRecords struct {
	BaseModel
	ChildID             int64     `json:"child_id" gorm:"column:child_id;not null;default:0" validate:"required"`                           // 孩子ID，关联children.id
	CampID              int64     `json:"camp_id" gorm:"column:camp_id;not null;default:0" validate:"required"`                             // 训练营ID，关联training_camps.id
	UserID              int64     `json:"user_id" gorm:"column:user_id;not null;default:0" validate:"required"`                             // 操作用户ID，关联users.id
	CheckinDate         time.Time `json:"checkin_date" gorm:"column:checkin_date;not null" validate:"required"`                             // 打卡日期
	PracticeDuration    int       `json:"practice_duration" gorm:"column:practice_duration;not null;default:0" validate:"required"`         // 练习时长（分钟）
	JumpCount1min       int       `json:"jump_count_1min" gorm:"column:jump_count_1min;not null;default:0" validate:"required"`             // 1分钟跳绳个数
	JumpCountContinuous int       `json:"jump_count_continuous" gorm:"column:jump_count_continuous;not null;default:0" validate:"required"` // 连续跳绳个数
	FeelingText         string    `json:"feeling_text" gorm:"column:feeling_text;size:65535" validate:"max=65535"`                          // 训练感受
	FeelingScore        int8      `json:"feeling_score" gorm:"column:feeling_score;not null;default:5" validate:"required"`                 // 感受评分 1-10
	Photos              string    `json:"photos" gorm:"column:photos"`                                                                      // 打卡照片URL数组
	PointsEarned        int       `json:"points_earned" gorm:"column:points_earned;not null;default:0" validate:"required"`                 // 获得积分
	Status              int8      `json:"status" gorm:"column:status;not null;default:1" validate:"required"`                               // 状态 1:正常 2:补卡
}

// TableName 指定表名
func (CheckinRecords) TableName() string {
	return "checkin_records"
}

// ==================== Requests ====================

// CheckinRecordsCreateRequest 创建打卡记录表：记录孩子每日训练打卡的详细信息请求
type CheckinRecordsCreateRequest struct {
	ChildID             int64     `json:"child_id" binding:"required" validate:"required"`              // 孩子ID，关联children.id
	CampID              int64     `json:"camp_id" binding:"required" validate:"required"`               // 训练营ID，关联training_camps.id
	UserID              int64     `json:"user_id" binding:"required" validate:"required"`               // 操作用户ID，关联users.id
	CheckinDate         time.Time `json:"checkin_date" binding:"required" validate:"required"`          // 打卡日期
	PracticeDuration    int       `json:"practice_duration" binding:"required" validate:"required"`     // 练习时长（分钟）
	JumpCount1min       int       `json:"jump_count_1min" binding:"required" validate:"required"`       // 1分钟跳绳个数
	JumpCountContinuous int       `json:"jump_count_continuous" binding:"required" validate:"required"` // 连续跳绳个数
	FeelingText         string    `json:"feeling_text" binding:"max=65535" validate:"max=65535"`        // 训练感受
	FeelingScore        int8      `json:"feeling_score" binding:"required" validate:"required"`         // 感受评分 1-10
	Photos              string    `json:"photos"`                                                       // 打卡照片URL数组
	PointsEarned        int       `json:"points_earned" binding:"required" validate:"required"`         // 获得积分
	Status              int8      `json:"status" binding:"required" validate:"required"`                // 状态 1:正常 2:补卡
}

// CheckinRecordsUpdateRequest 更新打卡记录表：记录孩子每日训练打卡的详细信息请求
type CheckinRecordsUpdateRequest struct {
	ChildID             *int64     `json:"child_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`              // 孩子ID，关联children.id
	CampID              *int64     `json:"camp_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`               // 训练营ID，关联training_camps.id
	UserID              *int64     `json:"user_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`               // 操作用户ID，关联users.id
	CheckinDate         *time.Time `json:"checkin_date,omitempty" binding:"omitempty,required" validate:"omitempty,required"`          // 打卡日期
	PracticeDuration    *int       `json:"practice_duration,omitempty" binding:"omitempty,required" validate:"omitempty,required"`     // 练习时长（分钟）
	JumpCount1min       *int       `json:"jump_count_1min,omitempty" binding:"omitempty,required" validate:"omitempty,required"`       // 1分钟跳绳个数
	JumpCountContinuous *int       `json:"jump_count_continuous,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 连续跳绳个数
	FeelingText         *string    `json:"feeling_text,omitempty" binding:"omitempty,max=65535" validate:"omitempty,max=65535"`        // 训练感受
	FeelingScore        *int8      `json:"feeling_score,omitempty" binding:"omitempty,required" validate:"omitempty,required"`         // 感受评分 1-10
	Photos              *string    `json:"photos,omitempty"`                                                                           // 打卡照片URL数组
	PointsEarned        *int       `json:"points_earned,omitempty" binding:"omitempty,required" validate:"omitempty,required"`         // 获得积分
	Status              *int8      `json:"status,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                // 状态 1:正常 2:补卡
}

// ==================== Responses ====================

// CheckinRecordsResponse 打卡记录表：记录孩子每日训练打卡的详细信息响应
type CheckinRecordsResponse struct {
	ID                  uint      `json:"id"`                    // 主键ID
	ChildID             int64     `json:"child_id"`              // 孩子ID，关联children.id
	CampID              int64     `json:"camp_id"`               // 训练营ID，关联training_camps.id
	UserID              int64     `json:"user_id"`               // 操作用户ID，关联users.id
	CheckinDate         time.Time `json:"checkin_date"`          // 打卡日期
	PracticeDuration    int       `json:"practice_duration"`     // 练习时长（分钟）
	JumpCount1min       int       `json:"jump_count_1min"`       // 1分钟跳绳个数
	JumpCountContinuous int       `json:"jump_count_continuous"` // 连续跳绳个数
	FeelingText         string    `json:"feeling_text"`          // 训练感受
	FeelingScore        int8      `json:"feeling_score"`         // 感受评分 1-10
	Photos              string    `json:"photos"`                // 打卡照片URL数组
	PointsEarned        int       `json:"points_earned"`         // 获得积分
	Status              int8      `json:"status"`                // 状态 1:正常 2:补卡
	CreatedAt           time.Time `json:"created_at"`            // 创建时间
	UpdatedAt           time.Time `json:"updated_at"`            // 更新时间
}

// CheckinRecordsListResponse 打卡记录表：记录孩子每日训练打卡的详细信息列表响应
type CheckinRecordsListResponse struct {
	List  []*CheckinRecordsResponse `json:"list"`  // 打卡记录表：记录孩子每日训练打卡的详细信息列表
	Total int64                     `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将打卡记录表：记录孩子每日训练打卡的详细信息模型转换为响应结构
func (m *CheckinRecords) ToResponse() *CheckinRecordsResponse {
	return &CheckinRecordsResponse{
		ID:                  m.ID,
		ChildID:             m.ChildID,
		CampID:              m.CampID,
		UserID:              m.UserID,
		CheckinDate:         m.CheckinDate,
		PracticeDuration:    m.PracticeDuration,
		JumpCount1min:       m.JumpCount1min,
		JumpCountContinuous: m.JumpCountContinuous,
		FeelingText:         m.FeelingText,
		FeelingScore:        m.FeelingScore,
		Photos:              m.Photos,
		PointsEarned:        m.PointsEarned,
		Status:              m.Status,
		CreatedAt:           m.CreatedAt,
		UpdatedAt:           m.UpdatedAt,
	}
}

// ToResponseList 将打卡记录表：记录孩子每日训练打卡的详细信息模型列表转换为响应列表
func CheckinRecordsToResponseList(models []*CheckinRecords, total int64) *CheckinRecordsListResponse {
	list := make([]*CheckinRecordsResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &CheckinRecordsListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到打卡记录表：记录孩子每日训练打卡的详细信息模型
func (m *CheckinRecords) ApplyUpdateRequest(req *CheckinRecordsUpdateRequest) {
	if req.ChildID != nil {
		m.ChildID = *req.ChildID
	}
	if req.CampID != nil {
		m.CampID = *req.CampID
	}
	if req.UserID != nil {
		m.UserID = *req.UserID
	}
	if req.CheckinDate != nil {
		m.CheckinDate = *req.CheckinDate
	}
	if req.PracticeDuration != nil {
		m.PracticeDuration = *req.PracticeDuration
	}
	if req.JumpCount1min != nil {
		m.JumpCount1min = *req.JumpCount1min
	}
	if req.JumpCountContinuous != nil {
		m.JumpCountContinuous = *req.JumpCountContinuous
	}
	if req.FeelingText != nil {
		m.FeelingText = *req.FeelingText
	}
	if req.FeelingScore != nil {
		m.FeelingScore = *req.FeelingScore
	}
	if req.Photos != nil {
		m.Photos = *req.Photos
	}
	if req.PointsEarned != nil {
		m.PointsEarned = *req.PointsEarned
	}
	if req.Status != nil {
		m.Status = *req.Status
	}
}
