package api

import (
	"kids-platform/internal/models"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/logger"
	"time"
)

// CheckinService 打卡服务接口
type CheckinService interface {
	// 创建打卡记录
	CreateCheckin(req *CheckinCreateRequest) (*CheckinResponse, error)
	// 获取打卡历史
	GetCheckinHistory(childID uint, campID uint, offset, limit int) (*CheckinHistoryResponse, error)
	// 获取今日打卡状态
	GetTodayCheckinStatus(childID uint, campID uint) (*TodayCheckinStatus, error)
	// 获取打卡统计（指定训练营）
	GetCheckinStats(childID uint, campID uint) (*CheckinStatsResponse, error)
	// 获取打卡统计（所有训练营）
	GetCheckinStatsAll(childID uint) (*CheckinStatsResponse, error)
}

// checkinService 打卡服务实现
type checkinService struct {
	checkinRecordsRepo        repositories.CheckinRecordsRepository
	userCampParticipationRepo repositories.UserCampParticipationsRepository
	trainingCampsRepo         repositories.TrainingCampsRepository
}

// NewCheckinService 创建打卡服务
func NewCheckinService(
	checkinRecordsRepo repositories.CheckinRecordsRepository,
	userCampParticipationRepo repositories.UserCampParticipationsRepository,
	trainingCampsRepo repositories.TrainingCampsRepository,
) CheckinService {
	return &checkinService{
		checkinRecordsRepo:        checkinRecordsRepo,
		userCampParticipationRepo: userCampParticipationRepo,
		trainingCampsRepo:         trainingCampsRepo,
	}
}

// ==================== 请求和响应结构定义 ====================

// CheckinCreateRequest 创建打卡请求
type CheckinCreateRequest struct {
	ChildID             uint     `json:"child_id" binding:"required"`          // 孩子ID
	CampID              uint     `json:"camp_id" binding:"required"`           // 训练营ID
	UserID              uint     `json:"user_id" binding:"required"`           // 操作用户ID
	PracticeDuration    int      `json:"practice_duration" binding:"required"` // 练习时长（分钟）
	JumpCount1min       int      `json:"jump_count_1min"`                      // 1分钟跳绳个数
	JumpCountContinuous int      `json:"jump_count_continuous"`                // 连续跳绳个数
	FeelingText         string   `json:"feeling_text"`                         // 训练感受
	FeelingScore        int8     `json:"feeling_score" binding:"min=1,max=10"` // 感受评分 1-10
	Photos              []string `json:"photos"`                               // 打卡照片URL数组
}

// CheckinResponse 打卡响应
type CheckinResponse struct {
	*models.CheckinRecordsResponse
	PointsEarned int    `json:"points_earned"` // 获得积分
	Message      string `json:"message"`       // 响应消息
}

// CheckinHistoryResponse 打卡历史响应
type CheckinHistoryResponse struct {
	List  []*models.CheckinRecordsResponse `json:"list"`  // 打卡记录列表
	Total int64                            `json:"total"` // 总数
}

// TodayCheckinStatus 今日打卡状态
type TodayCheckinStatus struct {
	HasCheckedIn bool                           `json:"has_checked_in"` // 是否已打卡
	CheckinData  *models.CheckinRecordsResponse `json:"checkin_data"`   // 打卡数据（如果已打卡）
}

// CheckinStatsResponse 打卡统计响应
type CheckinStatsResponse struct {
	TotalCheckins      int     `json:"total_checkins"`       // 总打卡次数
	ConsecutiveDays    int     `json:"consecutive_days"`     // 连续打卡天数
	MaxConsecutiveDays int     `json:"max_consecutive_days"` // 最大连续天数
	TotalStudyMinutes  int     `json:"total_study_minutes"`  // 总学习时长
	AverageFeeling     float64 `json:"average_feeling"`      // 平均感受评分
	ThisWeekCheckins   int     `json:"this_week_checkins"`   // 本周打卡次数
	ThisMonthCheckins  int     `json:"this_month_checkins"`  // 本月打卡次数
}

// ==================== 服务方法实现 ====================

// CreateCheckin 创建打卡记录
func (s *checkinService) CreateCheckin(req *CheckinCreateRequest) (*CheckinResponse, error) {
	// 检查今日是否已打卡
	today := time.Now().Format("2006-01-02")
	todayDate, _ := time.Parse("2006-01-02", today)

	existing, err := s.checkinRecordsRepo.GetByChildAndDate(req.ChildID, todayDate)
	if err != nil && err != errcode.ErrDataNotFound {
		logger.Error("Failed to check existing checkin", "child_id", req.ChildID, "date", today, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("检查今日打卡状态失败")
	}

	if existing != nil {
		return nil, errcode.ErrValidation.WithDetails("今日已经打卡，不能重复打卡")
	}

	// 计算获得积分（基础积分 + 时长奖励）
	pointsEarned := s.calculatePoints(req.PracticeDuration, req.JumpCount1min)

	// 创建打卡记录
	checkinRecord := &models.CheckinRecords{
		ChildID:             int64(req.ChildID),
		CampID:              int64(req.CampID),
		UserID:              int64(req.UserID),
		CheckinDate:         todayDate,
		PracticeDuration:    req.PracticeDuration,
		JumpCount1min:       req.JumpCount1min,
		JumpCountContinuous: req.JumpCountContinuous,
		FeelingText:         req.FeelingText,
		FeelingScore:        req.FeelingScore,
		PointsEarned:        pointsEarned,
		Status:              1, // 正常
	}

	// 处理照片数组（转换为JSON字符串）
	if len(req.Photos) > 0 {
		// 这里需要将[]string转换为JSON存储，暂时简化处理
		checkinRecord.Photos = req.Photos[0] // 暂时只存储第一张照片
	}

	if err := s.checkinRecordsRepo.Create(checkinRecord); err != nil {
		logger.Error("Failed to create checkin record", "child_id", req.ChildID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("创建打卡记录失败")
	}

	// 更新训练营参与进度
	if err := s.updateCampProgress(req.ChildID, req.CampID, pointsEarned); err != nil {
		logger.Error("Failed to update camp progress", "child_id", req.ChildID, "camp_id", req.CampID, "error", err)
		// 进度更新失败不影响打卡成功，只记录错误
	}

	logger.Info("Checkin created successfully", "child_id", req.ChildID, "camp_id", req.CampID, "points", pointsEarned)

	return &CheckinResponse{
		CheckinRecordsResponse: checkinRecord.ToResponse(),
		PointsEarned:           pointsEarned,
		Message:                "打卡成功！",
	}, nil
}

// GetCheckinHistory 获取打卡历史
func (s *checkinService) GetCheckinHistory(childID uint, campID uint, offset, limit int) (*CheckinHistoryResponse, error) {
	records, total, err := s.checkinRecordsRepo.GetByChildIDAndCampID(childID, campID, offset, limit)
	if err != nil {
		logger.Error("Failed to get checkin history", "child_id", childID, "camp_id", campID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取打卡历史失败")
	}

	var responseList []*models.CheckinRecordsResponse
	for _, record := range records {
		responseList = append(responseList, record.ToResponse())
	}

	logger.Info("Checkin history retrieved successfully", "child_id", childID, "camp_id", campID, "total", total)
	return &CheckinHistoryResponse{
		List:  responseList,
		Total: total,
	}, nil
}

// GetTodayCheckinStatus 获取今日打卡状态
func (s *checkinService) GetTodayCheckinStatus(childID uint, campID uint) (*TodayCheckinStatus, error) {
	today := time.Now().Format("2006-01-02")
	todayDate, _ := time.Parse("2006-01-02", today)

	checkin, err := s.checkinRecordsRepo.GetByChildAndDate(childID, todayDate)
	if err != nil && err != errcode.ErrDataNotFound {
		logger.Error("Failed to get today checkin status", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取今日打卡状态失败")
	}

	status := &TodayCheckinStatus{
		HasCheckedIn: checkin != nil,
	}

	if checkin != nil {
		status.CheckinData = checkin.ToResponse()
	}

	return status, nil
}

// GetCheckinStats 获取打卡统计
func (s *checkinService) GetCheckinStats(childID uint, campID uint) (*CheckinStatsResponse, error) {
	// 获取指定训练营的所有打卡记录用于统计
	records, _, err := s.checkinRecordsRepo.GetByChildIDAndCampID(childID, campID, 0, 1000) // 获取足够多的记录用于统计
	if err != nil {
		logger.Error("Failed to get checkin records for stats", "child_id", childID, "camp_id", campID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取打卡统计失败")
	}

	stats := &CheckinStatsResponse{
		TotalCheckins:      len(records),
		ConsecutiveDays:    s.calculateConsecutiveDays(records),
		MaxConsecutiveDays: s.calculateMaxConsecutiveDays(records),
		TotalStudyMinutes:  s.calculateTotalStudyMinutes(records),
		AverageFeeling:     s.calculateAverageFeeling(records),
		ThisWeekCheckins:   s.calculateThisWeekCheckins(records),
		ThisMonthCheckins:  s.calculateThisMonthCheckins(records),
	}

	logger.Info("Checkin stats calculated successfully", "child_id", childID, "camp_id", campID, "total_checkins", stats.TotalCheckins)
	return stats, nil
}

// GetCheckinStatsAll 获取打卡统计（所有训练营）
func (s *checkinService) GetCheckinStatsAll(childID uint) (*CheckinStatsResponse, error) {
	// 获取所有打卡记录用于统计
	records, _, err := s.checkinRecordsRepo.GetByChildID(childID, 0, 1000) // 获取足够多的记录用于统计
	if err != nil {
		logger.Error("Failed to get checkin records for stats", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取打卡统计失败")
	}

	stats := &CheckinStatsResponse{
		TotalCheckins:      len(records),
		ConsecutiveDays:    s.calculateConsecutiveDays(records),
		MaxConsecutiveDays: s.calculateMaxConsecutiveDays(records),
		TotalStudyMinutes:  s.calculateTotalStudyMinutes(records),
		AverageFeeling:     s.calculateAverageFeeling(records),
		ThisWeekCheckins:   s.calculateThisWeekCheckins(records),
		ThisMonthCheckins:  s.calculateThisMonthCheckins(records),
	}

	logger.Info("Checkin stats (all camps) calculated successfully", "child_id", childID, "total_checkins", stats.TotalCheckins)
	return stats, nil
}

// ==================== 辅助方法 ====================

// calculatePoints 计算打卡获得的积分
func (s *checkinService) calculatePoints(duration int, jumpCount int) int {
	basePoints := 10                  // 基础积分
	durationBonus := duration / 5 * 2 // 每5分钟额外2积分
	jumpBonus := jumpCount / 50 * 5   // 每50个跳绳额外5积分

	total := basePoints + durationBonus + jumpBonus
	if total > 100 {
		total = 100 // 单次打卡最多100积分
	}

	return total
}

// updateCampProgress 更新训练营参与进度
func (s *checkinService) updateCampProgress(childID uint, campID uint, pointsEarned int) error {
	participation, err := s.userCampParticipationRepo.GetByCampAndChild(campID, childID)
	if err != nil {
		return err
	}

	// 更新统计数据
	participation.TotalCheckins++
	participation.TotalStudyMinutes += pointsEarned // 这里需要传入实际的学习时长

	// 计算进度百分比
	camp, err := s.trainingCampsRepo.GetByID(campID)
	if err == nil {
		participation.ProgressPercentage = float64(participation.TotalCheckins) / float64(camp.DurationDays) * 100
		if participation.ProgressPercentage > 100 {
			participation.ProgressPercentage = 100
		}
	}

	// 更新当前天数
	if participation.TotalCheckins > participation.CurrentDay {
		participation.CurrentDay = participation.TotalCheckins
	}

	return s.userCampParticipationRepo.Update(participation.ID, participation)
}

// calculateConsecutiveDays 计算连续打卡天数
func (s *checkinService) calculateConsecutiveDays(records []*models.CheckinRecords) int {
	if len(records) == 0 {
		return 0
	}

	// 按日期排序（最新的在前）
	// 这里简化处理，假设records已经按日期排序
	consecutive := 1

	for i := 0; i < len(records)-1; i++ {
		current := records[i].CheckinDate
		next := records[i+1].CheckinDate

		// 检查是否连续
		if current.Sub(next).Hours() <= 24 {
			consecutive++
		} else {
			break
		}
	}

	return consecutive
}

// calculateMaxConsecutiveDays 计算最大连续天数
func (s *checkinService) calculateMaxConsecutiveDays(records []*models.CheckinRecords) int {
	// 简化实现，返回当前连续天数
	return s.calculateConsecutiveDays(records)
}

// calculateTotalStudyMinutes 计算总学习时长
func (s *checkinService) calculateTotalStudyMinutes(records []*models.CheckinRecords) int {
	total := 0
	for _, record := range records {
		total += record.PracticeDuration
	}
	return total
}

// calculateAverageFeeling 计算平均感受评分
func (s *checkinService) calculateAverageFeeling(records []*models.CheckinRecords) float64 {
	if len(records) == 0 {
		return 0
	}

	total := 0
	for _, record := range records {
		total += int(record.FeelingScore)
	}

	return float64(total) / float64(len(records))
}

// calculateThisWeekCheckins 计算本周打卡次数
func (s *checkinService) calculateThisWeekCheckins(records []*models.CheckinRecords) int {
	now := time.Now()
	weekStart := now.AddDate(0, 0, -int(now.Weekday()))

	count := 0
	for _, record := range records {
		if record.CheckinDate.After(weekStart) {
			count++
		}
	}

	return count
}

// calculateThisMonthCheckins 计算本月打卡次数
func (s *checkinService) calculateThisMonthCheckins(records []*models.CheckinRecords) int {
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	count := 0
	for _, record := range records {
		if record.CheckinDate.After(monthStart) {
			count++
		}
	}

	return count
}
