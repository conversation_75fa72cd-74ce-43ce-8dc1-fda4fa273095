// 打卡表单页面 - 支持今日打卡和补打卡
const {
  childrenActions,
  startLoading,
  endLoading,
  isLoading,
  userActions,
  eventBus,
  EVENT_NAMES,
} = require("../../../utils/index.js");

const checkinAPI = require("../../../apis/checkin.js");
const contentAPI = require("../../../apis/content.js");

Page({
  data: {
    // 页面参数
    campId: null,
    checkinType: "today", // today: 今日打卡, makeup: 补打卡
    checkinDate: null, // 打卡日期
    fromPage: null, // 来源页面

    // 加载状态
    loading: true,
    campLoading: false,

    // 当前孩子信息
    currentChild: null,
    userId: null,

    // 训练营信息
    campInfo: {
      id: 1,
      title: "21天跳绳养成计划",
      currentDay: 14,
      totalDays: 21,
      todayTask: "连续跳绳50个",
    },

    // 表单数据
    duration: 15, // 练习时长
    showCustomDuration: false, // 是否显示自定义时长
    customDuration: "", // 自定义时长
    score1min: "", // 1分钟跳绳成绩
    scoreContinuous: "", // 连续跳绳成绩
    feeling: "", // 训练感受
    photos: [], // 训练照片

    // 奖励积分
    rewardPoints: 25,

    // 是否有契约
    hasContract: true,

    // 契约进度
    contractProgress: {
      current: 14,
      total: 21,
      percent: 67,
      remaining: 7,
      isCompleted: false,
    },

    // 表单验证
    canSubmit: true,
    isSubmitting: false,

    // 成功弹窗
    showSuccessModal: false,
    earnedMedal: null, // 获得的勋章
  },

  onLoad(options) {
    console.log("打卡表单页面加载", options);

    // 解析页面参数
    const campId = options?.camp_id ? parseInt(options.camp_id) : null;
    const checkinType = options?.type || "today";
    const checkinDate = options?.date || new Date().toISOString().split("T")[0];
    const fromPage = options?.from || "unknown";

    // 获取用户ID
    const userId = userActions.getUserInfo().id;
    // 获取当前选择的孩子
    const currentChild = childrenActions.getCurrentChild();
    if (!currentChild || !currentChild.id) {
      wx.showToast({
        title: "请先选择孩子",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    // 验证必要参数
    if (!campId) {
      wx.showToast({
        title: "训练营参数缺失",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    this.setData({
      campId: campId,
      checkinType: checkinType,
      checkinDate: checkinDate,
      fromPage: fromPage,
      currentChild: currentChild,
      userId: userId,
      loading: true,
    });

    // 加载数据
    this.loadAllData();
    this.validateForm();
  },

  /**
   * 加载所有数据
   */
  async loadAllData() {
    try {
      this.setData({ loading: true });

      // 并行加载数据
      await Promise.all([this.loadCampInfo(), this.loadUserCampStatus()]);

      this.setData({ loading: false });
    } catch (error) {
      console.error("加载数据失败:", error);
      this.setData({ loading: false });

      wx.showToast({
        title: "数据加载失败",
        icon: "none",
      });
    }
  },

  /**
   * 加载训练营信息
   */
  async loadCampInfo() {
    try {
      this.setData({ campLoading: true });

      const campDetail = await contentAPI.getCampDetail(this.data.campId);
      console.log("训练营详情:", campDetail);

      // 转换数据格式
      const transformedCampInfo = {
        id: campDetail.id,
        title: campDetail.title || "训练营",
        subtitle: campDetail.subtitle || "",
        totalDays: campDetail.duration_days || 21,
        todayTask: campDetail.daily_task || "完成今日训练",
        currentDay: 1, // 这个值从用户参与状态中获取
      };

      this.setData({
        campInfo: transformedCampInfo,
        campLoading: false,
      });
    } catch (error) {
      console.error("加载训练营详情失败:", error);
      this.setData({ campLoading: false });

      // 使用默认数据
      this.loadDefaultCampInfo();
    }
  },

  /**
   * 加载用户训练营参与状态
   */
  async loadUserCampStatus() {
    try {
      const userCamps = await contentAPI.getUserCamps();
      console.log("用户训练营列表:", userCamps);

      // 找到当前训练营的参与状态
      const currentCamp = userCamps.find(
        (camp) => camp.camp_id === this.data.campId
      );

      if (currentCamp) {
        // 更新训练营进度信息
        const campInfo = { ...this.data.campInfo };
        campInfo.currentDay = currentCamp.current_day || 1;

        this.setData({ campInfo });
      }
    } catch (error) {
      console.error("加载用户训练营状态失败:", error);
    }
  },

  /**
   * 加载默认训练营信息
   */
  loadDefaultCampInfo() {
    console.log("使用默认训练营信息");
    this.setData({
      campInfo: {
        id: this.data.campId || 1,
        title: "21天跳绳挑战",
        subtitle: "从零基础到连续跳绳300个",
        currentDay: 8,
        totalDays: 21,
        todayTask: "连续跳绳50个",
      },
    });
  },

  /**
   * 选择练习时长
   */
  selectDuration(e) {
    const duration = parseInt(e.currentTarget.dataset.duration);
    this.setData({
      duration,
      showCustomDuration: false, // 选择预设时长时隐藏自定义输入
    });
    this.validateForm();
  },

  /**
   * 切换自定义时长显示
   */
  toggleCustomDuration() {
    this.setData({
      showCustomDuration: !this.data.showCustomDuration,
    });
  },

  /**
   * 自定义时长输入
   */
  onCustomDurationInput(e) {
    const customDuration = e.detail.value;
    this.setData({
      customDuration,
      duration: customDuration ? parseInt(customDuration) : 0,
    });
    this.validateForm();
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 1分钟跳绳成绩输入
   */
  onScore1minInput(e) {
    this.setData({
      score1min: e.detail.value,
    });
  },

  /**
   * 连续跳绳成绩输入
   */
  onScoreContinuousInput(e) {
    this.setData({
      scoreContinuous: e.detail.value,
    });
  },

  /**
   * 训练感受输入
   */
  onFeelingInput(e) {
    this.setData({
      feeling: e.detail.value,
    });
  },

  /**
   * 添加照片
   */
  addPhoto() {
    const that = this;
    wx.chooseImage({
      count: 3 - this.data.photos.length,
      sizeType: ["compressed"],
      sourceType: ["album", "camera"],
      success: (res) => {
        const photos = [...this.data.photos, ...res.tempFilePaths];
        that.setData({ photos });
      },
    });
  },

  /**
   * 预览照片
   */
  previewPhoto(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.photos[index],
      urls: this.data.photos,
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { duration } = this.data;
    const canSubmit = duration > 0;
    this.setData({ canSubmit });
  },

  /**
   * 提交打卡
   */
  async submitCheckin() {
    if (!this.data.canSubmit || this.data.isSubmitting) {
      return;
    }

    if (!this.data.currentChild || !this.data.currentChild.id) {
      wx.showToast({
        title: "孩子信息缺失",
        icon: "none",
      });
      return;
    }

    const loadingKey = "submit-checkin";

    try {
      startLoading(loadingKey, {
        title: "提交打卡中...",
        timeout: 15000,
      });

      this.setData({ isSubmitting: true });

      console.log("🌐 开始提交打卡记录");

      // 构建打卡数据
      const checkinData = {
        user_id: this.data.userId,
        child_id: this.data.currentChild.id,
        camp_id: this.data.campId,
        practice_duration: this.data.duration,
        jump_count_1min: parseInt(this.data.score1min) || 0,
        jump_count_continuous: parseInt(this.data.scoreContinuous) || 0,
        feeling_text:
          this.data.feeling ||
          (this.data.checkinType === "makeup" ? "补卡完成" : "今日训练完成"),
        feeling_score: 5, // 默认感受评分为5分（满分10分）
        checkin_date: this.data.checkinDate,
        status: this.data.checkinType === "makeup" ? 2 : 1, // 1:正常打卡 2:补打卡
        photos: this.data.photos || [],
      };

      console.log("🌐 提交打卡数据:", checkinData);

      // 调用API提交打卡
      const response = await checkinAPI.createCheckin(checkinData);

      console.log("✅ 打卡提交成功:", response);

      endLoading(loadingKey, true);

      // 更新训练营标题显示API标识
      this.setData({
        "campInfo.title": `🌐 ${this.data.campInfo.title.replace(
          /^🌐\s*/,
          ""
        )}`,
        dataSourceInfo: `✅ API打卡提交成功 - ${new Date().toLocaleTimeString()}`,
      });

      this.handleCheckinSuccess(response.data);
    } catch (error) {
      console.error("❌ 打卡提交失败:", error);

      endLoading(loadingKey, false);

      // 确保错误状态也被正确重置
      setTimeout(() => {
        this.setData({
          isSubmitting: false,
          dataSourceInfo: `⚠️ API打卡提交失败 - ${new Date().toLocaleTimeString()}`,
        });

        console.log("❌ 打卡失败，状态已重置");
      }, 100);

      wx.showToast({
        title: "打卡失败，请重试",
        icon: "none",
      });
    }
  },

  /**
   * 处理打卡成功
   */
  handleCheckinSuccess(responseData) {
    console.log("🎉 处理打卡成功", responseData);

    // 保存打卡状态
    const checkinDate = this.data.checkinDate;
    wx.setStorageSync(`lastCheckinDate_${checkinDate}`, checkinDate);

    // 更新契约进度
    let contractProgress = { ...this.data.contractProgress };
    if (this.data.hasContract) {
      contractProgress.current += 1;
      contractProgress.percent = Math.round(
        (contractProgress.current / contractProgress.total) * 100
      );
      contractProgress.remaining =
        contractProgress.total - contractProgress.current;
      contractProgress.isCompleted =
        contractProgress.current >= contractProgress.total;
    }

    // 检查是否获得勋章
    let earnedMedal = null;
    if (contractProgress.current === 7) {
      earnedMedal = { name: "坚持一周", icon: "🏅" };
    } else if (contractProgress.current === 14) {
      earnedMedal = { name: "坚持两周", icon: "🏆" };
    } else if (contractProgress.isCompleted) {
      earnedMedal = { name: "契约守护者", icon: "👑" };
    }

    // 确保所有加载状态都被正确重置
    setTimeout(() => {
      this.setData({
        isSubmitting: false,
        showSuccessModal: true,
        contractProgress,
        earnedMedal,
      });

      console.log("✅ 成功弹窗已显示，所有状态已重置");

      // 触发详情页面刷新
      this.notifyDetailPageRefresh();

      // 发送全局事件通知
      this.emitCheckinSuccessEvent();
    }, 100); // 延迟100ms确保状态更新完成
  },

  /**
   * 通知详情页面刷新数据
   */
  notifyDetailPageRefresh() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];

      // 检查是否是详情页面
      if (prevPage.route.includes("growth/detail/detail")) {
        console.log("🔄 通知详情页面刷新数据");

        // 调用详情页面的刷新方法
        if (typeof prevPage.refreshPageData === "function") {
          prevPage.refreshPageData();
        } else {
          // 兼容旧版本，直接更新状态
          if (this.data.checkinType === "today") {
            prevPage.setData({
              todayStatus: "completed",
            });
          }
        }
      }
    }
  },

  /**
   * 发送全局打卡成功事件
   */
  emitCheckinSuccessEvent() {
    const eventData = {
      campId: this.data.campId,
      childId: this.data.currentChild?.id,
      checkinType: this.data.checkinType,
      checkinDate: this.data.checkinDate,
      timestamp: Date.now(),
    };

    console.log("📡 发送全局打卡成功事件", eventData);

    // 使用事件总线发送事件
    eventBus.emit(EVENT_NAMES.CHECKIN_SUCCESS, eventData);

    // 兼容旧版本：同时保存到app全局数据
    const app = getApp();
    if (app.globalData) {
      app.globalData.lastCheckinEvent = eventData;
    }
  },

  /**
   * 关闭成功弹窗
   */
  closeSuccessModal() {
    this.setData({
      showSuccessModal: false,
    });

    // 返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 300);
  },

  /**
   * 生成海报
   */
  generatePoster() {
    this.setData({
      showSuccessModal: false,
    });

    // 构造海报数据
    const posterData = {
      type: "checkin",
      camp_id: this.data.campInfo.id,
      campTitle: this.data.campInfo.title,
      currentDay: this.data.campInfo.currentDay,
      totalDays: this.data.campInfo.totalDays,
      todayTask: this.data.campInfo.todayTask,
      checkinTime: new Date().toISOString(),
    };

    // 跳转到海报页面
    wx.navigateTo({
      url: `/pages/social/share-poster/share-poster?type=checkin&data=${encodeURIComponent(
        JSON.stringify(posterData)
      )}`,
    });
  },

  /**
   * 页面卸载时清理
   */
  onUnload() {
    // 清理定时器等资源
  },
});
