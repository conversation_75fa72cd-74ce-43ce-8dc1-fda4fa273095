<!-- Token调试页面 -->
<view class="debug-container">
  <view class="debug-header">
    <text class="debug-title">🔍 Token调试工具</text>
    <text class="debug-subtitle">诊断Token验证问题</text>
  </view>

  <!-- Token信息 -->
  <view class="info-section">
    <view class="section-title">📋 Token信息</view>
    <view class="info-item">
      <text class="info-label">登录状态:</text>
      <text class="info-value {{tokenInfo.isLoggedIn ? 'success' : 'error'}}">
        {{tokenInfo.isLoggedIn ? '已登录' : '未登录'}}
      </text>
    </view>
    <view class="info-item">
      <text class="info-label">Token长度:</text>
      <text class="info-value">{{tokenInfo.tokenLength}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">Token预览:</text>
      <text class="info-value token-preview">{{tokenInfo.tokenPreview}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="debug-btn primary" bindtap="analyzeToken">
      分析Token格式
    </button>
    <button class="debug-btn info" bindtap="testApiCall">
      测试API调用
    </button>
    <button class="debug-btn warning" bindtap="clearToken">
      清除Token
    </button>
    <button class="debug-btn success" bindtap="reLogin">
      重新登录
    </button>
    <button class="debug-btn secondary" bindtap="clearResults">
      清空结果
    </button>
  </view>

  <!-- 调试结果 -->
  <view class="debug-results" wx:if="{{debugResults.length > 0}}">
    <view class="results-header">
      <text class="results-title">📊 调试结果</text>
    </view>
    
    <view class="result-item" wx:for="{{debugResults}}" wx:key="id">
      <view class="result-header">
        <text class="result-action">{{item.action}}</text>
        <text class="result-time">{{item.time}}</text>
      </view>
      <view class="result-type type-{{item.type}}">
        <text wx:if="{{item.type === 'success'}}">✅ 成功</text>
        <text wx:elif="{{item.type === 'error'}}">❌ 错误</text>
        <text wx:elif="{{item.type === 'info'}}">ℹ️ 信息</text>
        <text wx:else>{{item.type}}</text>
      </view>
      <view class="result-message">{{item.message}}</view>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="debug-instructions">
    <view class="instruction-title">📖 使用说明</view>
    <view class="instruction-item">
      <text class="instruction-label">分析Token:</text>
      <text class="instruction-text">检查JWT Token格式和内容</text>
    </view>
    <view class="instruction-item">
      <text class="instruction-label">测试API:</text>
      <text class="instruction-text">使用当前Token调用验证API</text>
    </view>
    <view class="instruction-item">
      <text class="instruction-label">清除Token:</text>
      <text class="instruction-text">清除本地存储的Token</text>
    </view>
    <view class="instruction-item">
      <text class="instruction-label">重新登录:</text>
      <text class="instruction-text">跳转到登录页面重新获取Token</text>
    </view>
  </view>
</view>
