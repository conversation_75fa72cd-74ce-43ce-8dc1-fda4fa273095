/* Token调试页面样式 */
.debug-container {
  padding: 32rpx;
  background-color: #F7F8FA;
  min-height: 100vh;
}

.debug-header {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.debug-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 16rpx;
}

.debug-subtitle {
  font-size: 26rpx;
  color: #666666;
  display: block;
}

.info-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
  align-items: flex-start;
}

.info-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #666666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  word-break: break-all;
}

.info-value.success {
  color: #4CAF50;
}

.info-value.error {
  color: #F44336;
}

.token-preview {
  font-family: monospace;
  font-size: 24rpx;
  background-color: #F5F5F5;
  padding: 8rpx;
  border-radius: 4rpx;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-bottom: 48rpx;
}

.debug-btn {
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  color: #FFFFFF;
}

.debug-btn.primary {
  background-color: #FF7A45;
}

.debug-btn.info {
  background-color: #4A90E2;
}

.debug-btn.warning {
  background-color: #FFA726;
}

.debug-btn.success {
  background-color: #66BB6A;
}

.debug-btn.secondary {
  background-color: #9E9E9E;
  grid-column: span 2;
}

.debug-results {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.results-header {
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.result-item {
  margin-bottom: 24rpx;
  padding: 24rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  border-left: 6rpx solid #E0E0E0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.result-action {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.result-time {
  font-size: 24rpx;
  color: #999999;
}

.result-type {
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.type-success {
  color: #4CAF50;
}

.type-error {
  color: #F44336;
}

.type-info {
  color: #2196F3;
}

.result-message {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  word-break: break-all;
  font-family: monospace;
  background-color: #F0F0F0;
  padding: 12rpx;
  border-radius: 6rpx;
}

.debug-instructions {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.instruction-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.instruction-item {
  display: flex;
  margin-bottom: 16rpx;
  align-items: flex-start;
}

.instruction-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #FF7A45;
  width: 160rpx;
  flex-shrink: 0;
}

.instruction-text {
  font-size: 26rpx;
  color: #666666;
  flex: 1;
  line-height: 1.4;
}
