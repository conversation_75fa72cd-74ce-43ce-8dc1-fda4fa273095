// Token调试页面
const { userActions, userStorage } = require("../../utils/index.js");

Page({
  data: {
    tokenInfo: {},
    userInfo: {},
    debugResults: []
  },

  onLoad() {
    console.log('Token调试页面启动');
    this.loadTokenInfo();
  },

  // 加载Token信息
  loadTokenInfo() {
    const token = userActions.getToken();
    const userInfo = userActions.getUserInfo();
    const isLoggedIn = userActions.isLoggedIn();
    
    // 直接从storage获取
    const storageToken = userStorage.getToken();
    const storageUserInfo = userStorage.getUserInfo();

    this.setData({
      tokenInfo: {
        stateToken: token,
        storageToken: storageToken,
        tokenLength: token ? token.length : 0,
        tokenPreview: token ? token.substring(0, 50) + '...' : '无',
        isLoggedIn: isLoggedIn
      },
      userInfo: {
        stateUserInfo: userInfo,
        storageUserInfo: storageUserInfo
      }
    });

    this.addDebugResult('Token信息加载', 'info', `Token长度: ${token ? token.length : 0}`);
  },

  // 分析Token格式
  analyzeToken() {
    const token = userActions.getToken();
    
    if (!token) {
      this.addDebugResult('Token分析', 'error', 'Token为空');
      return;
    }

    // JWT Token应该有3个部分，用.分隔
    const parts = token.split('.');
    
    this.addDebugResult('Token分析', 'info', `Token部分数量: ${parts.length}`);
    
    if (parts.length !== 3) {
      this.addDebugResult('Token格式', 'error', `JWT Token应该有3个部分，当前有${parts.length}个`);
    } else {
      this.addDebugResult('Token格式', 'success', 'JWT Token格式正确');
      
      // 尝试解析header
      try {
        const header = JSON.parse(atob(parts[0]));
        this.addDebugResult('Token Header', 'success', JSON.stringify(header));
      } catch (error) {
        this.addDebugResult('Token Header', 'error', '无法解析Header: ' + error.message);
      }
      
      // 尝试解析payload
      try {
        const payload = JSON.parse(atob(parts[1]));
        this.addDebugResult('Token Payload', 'success', JSON.stringify(payload));
        
        // 检查过期时间
        if (payload.exp) {
          const expDate = new Date(payload.exp * 1000);
          const now = new Date();
          const isExpired = expDate < now;
          
          this.addDebugResult('Token过期', isExpired ? 'error' : 'success', 
            `过期时间: ${expDate.toLocaleString()}, ${isExpired ? '已过期' : '未过期'}`);
        }
      } catch (error) {
        this.addDebugResult('Token Payload', 'error', '无法解析Payload: ' + error.message);
      }
    }
  },

  // 测试API调用
  async testApiCall() {
    this.addDebugResult('API测试', 'info', '开始测试API调用...');
    
    try {
      const { http } = require("../../utils/index.js");
      const result = await http.get('/api/v1/auth/verify');
      
      this.addDebugResult('API测试', 'success', 'API调用成功: ' + JSON.stringify(result));
    } catch (error) {
      this.addDebugResult('API测试', 'error', 'API调用失败: ' + error.message);
    }
  },

  // 清除Token
  clearToken() {
    userActions.logout();
    userStorage.clearUserData();
    
    this.addDebugResult('清除Token', 'success', 'Token已清除');
    this.loadTokenInfo();
  },

  // 重新登录
  reLogin() {
    wx.reLaunch({
      url: '/pages/login/login'
    });
  },

  // 添加调试结果
  addDebugResult(action, type, message) {
    const results = this.data.debugResults;
    results.push({
      id: Date.now(),
      action,
      type,
      message,
      time: new Date().toLocaleTimeString()
    });
    
    this.setData({
      debugResults: results
    });
  },

  // 清空调试结果
  clearResults() {
    this.setData({
      debugResults: []
    });
  }
});
