// 打卡相关API - 遵循MVS规则
// 处理打卡记录、历史查询、视频上传、社交互动等功能

const { http } = require("../utils/request.js");
const { API } = require("../utils/constants.js");

// 打卡相关API端点 - 模块内部定义
const ENDPOINTS = {
  CREATE: `${API.BASE_URL}/checkins`,
  HISTORY: `${API.BASE_URL}/checkins/history`,
  UPLOAD_VIDEO: `${API.BASE_URL}/checkins/upload`,
  DETAIL: `${API.BASE_URL}/checkins/:id`,
  DELETE: `${API.BASE_URL}/checkins/:id`,
  TODAY_STATUS: `${API.BASE_URL}/checkins/today`,
  STATS: `${API.BASE_URL}/checkins/stats`,
};

/**
 * 打卡API模块
 */
const checkinAPI = {
  /**
   * 创建打卡记录
   * @param {Object} checkinData 打卡数据
   * @param {number} checkinData.child_id 孩子ID
   * @param {number} checkinData.camp_id 训练营ID（可选）
   * @param {number} checkinData.practice_duration 练习时长（分钟）
   * @param {number} checkinData.score_1min 一分钟跳绳成绩（可选）
   * @param {number} checkinData.score_continuous 连续跳绳成绩（可选）
   * @param {string} checkinData.video_id 视频号视频ID（可选）
   * @param {string} checkinData.experience_text 经验感悟文字（可选）
   * @param {string} checkinData.checkin_date 打卡日期（可选，默认今天）
   * @param {number} checkinData.status 打卡状态（1:正常 2:补打卡，可选）
   * @param {Object} checkinData.location 位置信息（可选）
   * @returns {Promise<Object>} 创建结果
   * @example
   * const checkin = await checkinAPI.createCheckin({
   *   child_id: 123,
   *   camp_id: 456,
   *   practice_duration: 15,
   *   score_1min: 100,
   *   experience_text: '今天跳了100个'
   * });
   */
  async createCheckin(checkinData) {
    console.log("✅ 创建打卡记录:", checkinData);

    try {
      // 参数验证
      if (!checkinData.child_id) {
        throw new Error("孩子ID不能为空");
      }
      if (
        !checkinData.practice_duration ||
        checkinData.practice_duration <= 0
      ) {
        throw new Error("练习时长必须大于0");
      }

      // 设置默认值
      const submitData = {
        child_id: checkinData.child_id,
        practice_duration: checkinData.practice_duration,
        score_1min: checkinData.score_1min || 0,
        score_continuous: checkinData.score_continuous || 0,
        video_id: checkinData.video_id || "",
        experience_text: checkinData.experience_text || "",
        checkin_date:
          checkinData.checkin_date || new Date().toISOString().split("T")[0],
        status: checkinData.status || 1,
        ...checkinData,
      };

      const response = await http.post(ENDPOINTS.CREATE, submitData);

      console.log("✅ 打卡记录创建成功");
      return response;
    } catch (error) {
      console.error("❌ 打卡记录创建失败:", error);
      throw error;
    }
  },

  /**
   * 获取打卡历史
   * @param {Object} params 查询参数
   * @param {number} params.child_id 孩子ID（可选）
   * @param {number} params.page 页码（可选）
   * @param {number} params.limit 每页数量（可选）
   * @param {string} params.date_from 开始日期（可选）
   * @param {string} params.date_to 结束日期（可选）
   * @returns {Promise<Array>} 打卡历史
   * @example
   * const history = await checkinAPI.getCheckinHistory({
   *   child_id: 123,
   *   page: 1,
   *   limit: 20
   * });
   */
  async getCheckinHistory(params = {}) {
    console.log("📋 获取打卡历史:", params);

    try {
      const response = await http.get(ENDPOINTS.HISTORY, { params });

      console.log("✅ 获取打卡历史成功");
      return response;
    } catch (error) {
      console.error("❌ 获取打卡历史失败:", error);
      throw error;
    }
  },

  /**
   * 上传打卡视频
   * @param {string} filePath 视频文件路径
   * @param {Object} options 上传选项
   * @param {number} options.child_id 孩子ID
   * @param {string} options.content 打卡内容
   * @param {Function} options.onProgress 上传进度回调
   * @returns {Promise<Object>} 上传结果
   * @example
   * const result = await checkinAPI.uploadCheckinVideo(filePath, {
   *   child_id: 123,
   *   content: '今天的跳绳练习',
   *   onProgress: (progress) => console.log(progress)
   * });
   */
  async uploadCheckinVideo(filePath, options = {}) {
    console.log("📹 上传打卡视频:", { filePath, options });

    try {
      if (!filePath) {
        throw new Error("视频文件路径不能为空");
      }
      if (!options.child_id) {
        throw new Error("孩子ID不能为空");
      }

      const uploadOptions = {
        name: "video",
        formData: {
          child_id: options.child_id,
          content: options.content || "",
        },
      };

      // 如果有进度回调，添加到选项中
      if (options.onProgress) {
        uploadOptions.onProgress = options.onProgress;
      }

      const response = await http.upload(
        ENDPOINTS.UPLOAD_VIDEO,
        filePath,
        uploadOptions
      );

      console.log("✅ 打卡视频上传成功");
      return response;
    } catch (error) {
      console.error("❌ 打卡视频上传失败:", error);
      throw error;
    }
  },

  /**
   * 获取打卡详情
   * @param {number} checkinId 打卡ID
   * @returns {Promise<Object>} 打卡详情
   * @example
   * const detail = await checkinAPI.getCheckinDetail(123);
   */
  async getCheckinDetail(checkinId) {
    console.log("🔍 获取打卡详情:", checkinId);

    try {
      if (!checkinId) {
        throw new Error("打卡ID不能为空");
      }

      const url = ENDPOINTS.DETAIL.replace(":id", checkinId);
      const response = await http.get(url);

      console.log("✅ 获取打卡详情成功");
      return response;
    } catch (error) {
      console.error("❌ 获取打卡详情失败:", error);
      throw error;
    }
  },

  /**
   * 删除打卡记录
   * @param {number} checkinId 打卡ID
   * @returns {Promise<Object>} 删除结果
   * @example
   * await checkinAPI.deleteCheckin(123);
   */
  async deleteCheckin(checkinId) {
    console.log("🗑️ 删除打卡记录:", checkinId);

    try {
      if (!checkinId) {
        throw new Error("打卡ID不能为空");
      }

      const url = ENDPOINTS.DELETE.replace(":id", checkinId);
      const response = await http.delete(url);

      console.log("✅ 打卡记录删除成功");
      return response;
    } catch (error) {
      console.error("❌ 打卡记录删除失败:", error);
      throw error;
    }
  },

  /**
   * 获取今日打卡状态
   * @param {number} childId 孩子ID
   * @param {number} campId 训练营ID
   * @returns {Promise<Object>} 今日打卡状态
   * @example
   * const status = await checkinAPI.getTodayCheckinStatus(123, 456);
   */
  async getTodayCheckinStatus(childId, campId) {
    console.log("📅 获取今日打卡状态:", { childId, campId });

    try {
      if (!childId) {
        throw new Error("孩子ID不能为空");
      }
      if (!campId) {
        throw new Error("训练营ID不能为空");
      }

      const response = await http.get(ENDPOINTS.TODAY_STATUS, {
        params: { child_id: childId, camp_id: campId },
      });

      console.log("✅ 获取今日打卡状态成功");
      return response;
    } catch (error) {
      console.error("❌ 获取今日打卡状态失败:", error);
      throw error;
    }
  },

  /**
   * 获取打卡统计信息
   * @param {number} childId 孩子ID
   * @param {number} campId 训练营ID
   * @param {Object} params 查询参数
   * @param {string} params.period 统计周期 (week/month/year)
   * @returns {Promise<Object>} 统计信息
   * @example
   * const stats = await checkinAPI.getCheckinStats(123, 456, { period: 'month' });
   */
  async getCheckinStats(childId, campId, params = {}) {
    console.log("📊 获取打卡统计信息:", { childId, campId, params });

    try {
      if (!childId) {
        throw new Error("孩子ID不能为空");
      }
      if (!campId) {
        throw new Error("训练营ID不能为空");
      }

      const response = await http.get(ENDPOINTS.STATS, {
        params: { child_id: childId, camp_id: campId, ...params },
      });

      console.log("✅ 获取打卡统计信息成功");
      return response;
    } catch (error) {
      console.error("❌ 获取打卡统计信息失败:", error);
      throw error;
    }
  },
};

module.exports = checkinAPI;
