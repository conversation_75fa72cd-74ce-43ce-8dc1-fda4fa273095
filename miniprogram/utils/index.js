// 工具函数统一导出 - 重构后的统一入口
// 遵循MVS规则：统一代码模板，模块化设计，消除功能重复

// 导入基础工具模块
const constants = require("./constants.js");
const { ERROR_CODES, ERROR_MESSAGES, API, APP_CONFIG } = constants;
const storage = require("./storage.js");
const { userStorage, cacheStorage } = require("./storage.js");
const errorHandler = require("./error.js");
const {
  createError,
  handleNetworkError,
  handleWechatError,
} = require("./error.js");

// 导入专用工具模块
const common = require("./common.js");
const ui = require("./ui.js");
const navigation = require("./navigation.js");

// 导入完整功能模块（移除重复的简化版本）
const { http } = require("./request.js");
const wechat = require("./wechat.js");
const format = require("./format.js");
const validator = require("./validator.js");
const imageManager = require("./image-manager.js");
const avatar = require("./avatar.js");
const dataConverter = require("./data-converter.js");

// 导入加载管理器
const {
  loadingManager,
  startLoading,
  endLoading,
  isLoading,
} = require("./loading-manager.js");

// 导入API管理模块
const apis = require("../apis/index.js");
const {
  authAPI,
  userAPI,
  childrenAPI,
  checkinAPI,
  contentAPI,
  api,
} = require("../apis/index.js");

// 导入状态管理模块
const { stateManager, getState, setState } = require("./state-manager.js");
const {
  userActions,
  childrenActions,
  businessActions,
  uiActions,
  appActions,
} = require("./state-actions.js");
const {
  stateMixin,
  createStatePage,
  withState,
  withStatePreset,
  statePresets,
} = require("./state-mixin.js");

// 导入登录守卫模块
const {
  loginGuard,
  createPageWithLoginGuard,
  withLoginGuard,
  withLoginGuardPreset,
  loginGuardPresets,
} = require("./login-guard.js");

// 页面工具函数已移至ui.js，这里保留别名以确保向前兼容
const pageUtils = ui;

// 简化的微信API和HTTP客户端已移除，使用完整版本
// 保留别名以确保向前兼容
const utils = common;

// CommonJS导出 - 重构后的统一导出
module.exports = {
  // 基础工具模块
  constants,
  ERROR_CODES,
  ERROR_MESSAGES,
  API,
  APP_CONFIG,
  storage,
  userStorage,
  cacheStorage,
  errorHandler,
  createError,
  handleNetworkError,
  handleWechatError,

  // 专用工具模块
  common,
  utils, // 别名，指向common
  ui,
  pageUtils, // 别名，指向ui
  navigation,

  // 完整功能模块
  http,
  wechat,
  format,
  validator,
  validate: validator, // 别名
  imageManager,
  avatar,
  dataConverter,

  // API管理模块
  apis,
  api,
  authAPI,
  userAPI,
  childrenAPI,
  checkinAPI,
  contentAPI,

  // 状态管理模块
  stateManager,
  getState,
  setState,
  userActions,
  childrenActions,
  businessActions,
  uiActions,
  appActions,

  // 页面状态管理
  stateMixin,
  createStatePage,
  withState,
  withStatePreset,
  statePresets,

  // 登录守卫
  loginGuard,
  createPageWithLoginGuard,
  withLoginGuard,
  withLoginGuardPreset,
  loginGuardPresets,

  // 加载管理器
  loadingManager,
  startLoading,
  endLoading,
  isLoading,
};
