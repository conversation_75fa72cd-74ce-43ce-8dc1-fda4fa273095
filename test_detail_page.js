// 测试脚本：验证 detail 页面修复效果
// 在浏览器控制台中运行此脚本

console.log("🧪 开始测试 detail 页面修复效果");

// 测试1：检查 content API 是否还有 getCampVideos 方法
function testAPI() {
  console.log("📋 测试1：检查API方法");
  
  try {
    const contentAPI = require("../../../apis/content.js");
    
    if (typeof contentAPI.getCampVideos === 'function') {
      console.error("❌ getCampVideos 方法仍然存在，应该已被删除");
      return false;
    } else {
      console.log("✅ getCampVideos 方法已正确删除");
      return true;
    }
  } catch (error) {
    console.error("❌ API测试失败:", error);
    return false;
  }
}

// 测试2：模拟日历显示逻辑
function testCalendarLogic() {
  console.log("📅 测试2：日历显示逻辑");
  
  // 模拟当前日期和训练营开始日期
  const today = new Date();
  const currentWeekStart = getWeekStart(today);
  
  // 测试场景1：训练营开始日期在当前周
  const campStartInCurrentWeek = new Date(currentWeekStart);
  campStartInCurrentWeek.setDate(campStartInCurrentWeek.getDate() + 2); // 周三开始
  
  const result1 = testWeekDisplayLogic(today, campStartInCurrentWeek);
  console.log("场景1 - 训练营在当前周开始:", result1 ? "✅ 通过" : "❌ 失败");
  
  // 测试场景2：训练营开始日期在上周
  const campStartInLastWeek = new Date(currentWeekStart);
  campStartInLastWeek.setDate(campStartInLastWeek.getDate() - 5); // 上周开始
  
  const result2 = testWeekDisplayLogic(today, campStartInLastWeek);
  console.log("场景2 - 训练营在上周开始:", result2 ? "✅ 通过" : "❌ 失败");
  
  return result1 && result2;
}

// 获取周开始日期（周一）
function getWeekStart(date) {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1);
  return new Date(d.setDate(diff));
}

// 测试周显示逻辑
function testWeekDisplayLogic(today, campStartDate) {
  const currentWeekStart = getWeekStart(today);
  const campStartWeek = getWeekStart(campStartDate);
  
  let startWeekOffset = -1; // 默认显示上周+本周
  let endWeekOffset = 0;
  
  // 应用修复后的逻辑
  if (campStartWeek.getTime() >= currentWeekStart.getTime()) {
    startWeekOffset = 0; // 当前周
    endWeekOffset = 1; // 下周
  }
  
  console.log("日历显示逻辑:", {
    today: today.toDateString(),
    currentWeekStart: currentWeekStart.toDateString(),
    campStartDate: campStartDate.toDateString(),
    campStartWeek: campStartWeek.toDateString(),
    startWeekOffset,
    endWeekOffset,
    显示周期: startWeekOffset === 0 ? "当前周+下周" : "上周+本周"
  });
  
  // 验证逻辑正确性
  if (campStartWeek.getTime() >= currentWeekStart.getTime()) {
    // 训练营在当前周或之后开始，应该显示当前周+下周
    return startWeekOffset === 0 && endWeekOffset === 1;
  } else {
    // 训练营在当前周之前开始，应该显示上周+本周
    return startWeekOffset === -1 && endWeekOffset === 0;
  }
}

// 测试3：检查视频数据转换
function testVideoTransform() {
  console.log("🎬 测试3：视频数据转换");
  
  // 模拟API返回的视频数据
  const mockVideos = [
    {
      id: 1,
      title: "跳绳基础教学",
      custom_subtitle: "从零开始学跳绳",
      description: "详细的跳绳基础教学",
      duration: 300,
      video_url: "https://example.com/video1.mp4",
      thumbnail: "https://example.com/thumb1.jpg"
    },
    {
      id: 2,
      title: "进阶技巧",
      duration: 450,
      video_url: "https://example.com/video2.mp4",
      cover_image: "https://example.com/cover2.jpg"
    }
  ];
  
  // 模拟转换函数
  function transformVideosData(videos) {
    if (!videos || !Array.isArray(videos)) {
      return [];
    }

    return videos.map((video, index) => ({
      id: video.id,
      title: video.title || `视频${index + 1}`,
      subtitle: video.custom_subtitle || video.description || "",
      duration: formatDuration(video.duration),
      watched: video.watched || false,
      isPlaying: false,
      videoUrl: video.video_url,
      coverImage: video.cover_image || video.thumbnail,
    }));
  }
  
  function formatDuration(seconds) {
    if (!seconds || seconds <= 0) return "0:00";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }
  
  const result = transformVideosData(mockVideos);
  
  console.log("转换结果:", result);
  
  // 验证转换结果
  const isValid = result.length === 2 &&
    result[0].title === "跳绳基础教学" &&
    result[0].subtitle === "从零开始学跳绳" &&
    result[0].duration === "5:00" &&
    result[1].title === "进阶技巧" &&
    result[1].duration === "7:30";
  
  return isValid;
}

// 运行所有测试
function runAllTests() {
  console.log("🚀 开始运行所有测试");
  
  const test1 = testAPI();
  const test2 = testCalendarLogic();
  const test3 = testVideoTransform();
  
  console.log("\n📊 测试结果汇总:");
  console.log("API方法删除:", test1 ? "✅" : "❌");
  console.log("日历显示逻辑:", test2 ? "✅" : "❌");
  console.log("视频数据转换:", test3 ? "✅" : "❌");
  
  const allPassed = test1 && test2 && test3;
  console.log("\n🎯 总体结果:", allPassed ? "✅ 所有测试通过" : "❌ 部分测试失败");
  
  return allPassed;
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== 'undefined') {
  runAllTests();
}

// 导出测试函数供其他地方使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testAPI,
    testCalendarLogic,
    testVideoTransform,
    runAllTests
  };
}
